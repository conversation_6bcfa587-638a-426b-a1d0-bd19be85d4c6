import uuid, os
from typing import Dict, Any
from langgraph.graph import <PERSON><PERSON>raph, END, START
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.runnables.graph import MermaidD<PERSON>Method
from Conversion_Agent_Stage2.state import Stage2WorkflowState
from Conversion_Agent_Stage2.nodes import Stage2ProcessingNodes


class Stage2GraphBuilder:
    """
    Advanced Stage 2 workflow graph builder for QMigrator module updates.

    This class constructs and manages a sophisticated AI-driven workflow graph that handles
    the complete Stage 2 QMigrator module update process. It orchestrates multiple nodes
    including process type routing, feature identification, module updates, validation loops,
    and statement-by-statement processing using LangGraph for state management.

    Key Features:
        - Dual-path processing (QMigrator object-level vs QBook statement-level)
        - AI-driven feature identification and module analysis
        - Iterative module update loops with validation
        - Statement-by-statement processing with retry mechanisms
        - Comprehensive state management and audit trails
        - Support for complex module update scenarios with 5-attempt limits

    Workflow Architecture:
        The graph implements a sophisticated flow with validation loops at each critical step,
        ensuring high-quality module updates through iterative AI improvement and comprehensive testing.
        Supports both qmigrator (object-level) and qbook (statement-level) processing paths.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 workflow graph builder with AI language model integration.

        Sets up the core components for the QMigrator module update workflow including
        the LangGraph state management system, memory checkpointing for workflow persistence,
        and integration with the provided language model for AI-driven analysis.

        Args:
            llm: Initialized Language Model instance for AI-driven module analysis and updates.
                 Supports multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama)
                 configured for optimal QMigrator module update performance.

        Attributes:
            llm: The language model instance for AI operations
            builder: LangGraph StateGraph builder for workflow construction
            memory: MemorySaver for workflow state persistence and checkpointing
            nodes: Stage2ProcessingNodes instance for workflow operations
        """
        self.llm = llm
        self.builder = StateGraph(Stage2WorkflowState)
        self.memory = MemorySaver()
        self.nodes = Stage2ProcessingNodes(llm)

    def build_graph(self):
        """
        Configure the complete Stage 2 QMigrator module update workflow graph.

        Constructs a sophisticated AI-driven workflow with multiple validation loops and
        conditional routing for optimal QMigrator module updates. The graph includes
        14 specialized nodes that handle process type routing, feature identification,
        module updates, validation, and statement processing with iterative improvement.

        Workflow Architecture:
            1. Process Type Decision: Route between qmigrator/qbook processing paths
            2. QMigrator Path: Post-Stage1 → Map Features → Validate Features → Common Path
            3. QBook Path: Statement Processing → Common Path
            4. Common Path: Identify Features → Validate → Update → Test → Compare
            5. Statement Loop: Process all statements with 5-attempt retry mechanism
            6. Complete Processing: Finalize workflow and generate summary

        Conditional Routing:
            - Process type routing directs to appropriate processing path
            - Validation failures trigger feedback loops for AI improvement
            - Module update failures restart with updated code (max 5 attempts per statement)
            - Statement processing continues until all statements are processed
            - Successful completion or max attempts reached terminates the workflow

        Returns:
            StateGraph: Configured workflow builder ready for compilation and execution
        """
        # Create fresh builder instance to avoid cached nodes and ensure clean state
        self.builder = StateGraph(Stage2WorkflowState)
        self.conversion_nodes = Stage2ProcessingNodes(llm=self.llm)

        # Add all workflow nodes for comprehensive QMigrator module updates
        self.builder.add_node("process_type_decision", self.conversion_nodes.process_type_decision)
        self.builder.add_node("post_stage1_processing_qmigrator", self.conversion_nodes.post_stage1_processing_qmigrator)
        self.builder.add_node("map_feature_combinations", self.conversion_nodes.map_feature_combinations)
        self.builder.add_node("statement_level_processing_qbook", self.conversion_nodes.statement_level_processing_qbook)
        self.builder.add_node("identify_responsible_features", self.conversion_nodes.identify_responsible_features)

        # New 9-node pipeline replacing update_responsible_modules + apply_modules + compare_ai_statements
        self.builder.add_node("categorize_execution_modules", self.conversion_nodes.categorize_execution_modules)
        self.builder.add_node("execute_pre_features", self.conversion_nodes.execute_pre_features)
        self.builder.add_node("combine_driver_module", self.conversion_nodes.combine_driver_module)
        self.builder.add_node("enhance_driver_module", self.conversion_nodes.enhance_driver_module)
        self.builder.add_node("decompose_enhanced_module", self.conversion_nodes.decompose_enhanced_module)
        self.builder.add_node("validate_module_enhancement", self.conversion_nodes.validate_module_enhancement)
        self.builder.add_node("execute_complete_pipeline", self.conversion_nodes.execute_complete_pipeline)
        self.builder.add_node("ai_statement_comparison_pipeline", self.conversion_nodes.ai_statement_comparison_pipeline)
        self.builder.add_node("enhancement_iteration_control", self.conversion_nodes.enhancement_iteration_control)

        self.builder.add_node("more_statements_decision", self.conversion_nodes.more_statements_decision)
        self.builder.add_node("complete_processing", self.conversion_nodes.complete_processing)

        # Define linear workflow progression with validation checkpoints
        self.builder.add_edge(START, "process_type_decision")
        # self.builder.add_edge("process_type_decision", "post_stage1_processing_qmigrator")
        # self.builder.add_edge("post_stage1_processing_qmigrator", 'map_feature_combinations')
        # self.builder.add_edge("map_feature_combinations", "identify_responsible_features")
        # self.builder.add_edge("identify_responsible_features", "features_valid_decision")
        # self.builder.add_conditional_edges(
        #     "features_valid_decision",
        #     self.should_continue_responsible_features,
        #     {
        #         "continue": "identify_responsible_features",  # Retry with feedback
        #         "proceed": "update_responsible_modules"  # Continue to module updates
        #     }
        # )

        # self.builder.add_edge("apply_modules", "compare_ai_statements")
        # self.builder.add_conditional_edges(
        #     "compare_ai_statements",
        #     self.should_continue_ai_comparison,
        #     {
        #         "continue": "update_responsible_modules",  # Retry with AI feedback
        #         "proceed": "complete_processing"  # Continue to next statement
        #     }
        # )
        # self.builder.add_edge("complete_processing", END)
        
        # Process type decision routing
        self.builder.add_conditional_edges(
            "process_type_decision",
            self.should_route_to_qmigrator_or_qbook,
            {
                "qmigrator": "post_stage1_processing_qmigrator",  # Object-level processing
                "qbook": "statement_level_processing_qbook"  # Statement-level processing
            }
        )

        # QMigrator path progression - direct flow without validation
        self.builder.add_edge("post_stage1_processing_qmigrator", "map_feature_combinations")
        self.builder.add_edge("map_feature_combinations", "identify_responsible_features")

        # QBook path progression
        self.builder.add_edge("statement_level_processing_qbook", "identify_responsible_features")

        # New 9-node pipeline progression
        self.builder.add_edge("identify_responsible_features", "categorize_execution_modules")
        self.builder.add_edge("categorize_execution_modules", "execute_pre_features")
        self.builder.add_edge("execute_pre_features", "combine_driver_module")
        self.builder.add_edge("combine_driver_module", "enhance_driver_module")
        self.builder.add_edge("enhance_driver_module", "decompose_enhanced_module")
        self.builder.add_edge("decompose_enhanced_module", "validate_module_enhancement")
        # Note: validate_module_enhancement has conditional edge below
        # Note: execute_complete_pipeline has conditional edge below
        self.builder.add_edge("ai_statement_comparison_pipeline", "enhancement_iteration_control")

        # Feedback loops - all target enhance_driver_module for centralized enhancement
        self.builder.add_conditional_edges(
            "validate_module_enhancement",
            self.should_retry_enhancement_from_validation,
            {
                "retry": "enhance_driver_module",  # Validation failed - retry enhancement
                "proceed": "execute_complete_pipeline"  # Validation passed - continue
            }
        )

        self.builder.add_conditional_edges(
            "execute_complete_pipeline",
            self.should_retry_enhancement_from_execution,
            {
                "retry": "enhance_driver_module",  # Execution failed - retry enhancement
                "proceed": "ai_statement_comparison_pipeline"  # Execution passed - continue
            }
        )

        # Enhancement iteration control with feedback loop
        self.builder.add_conditional_edges(
            "enhancement_iteration_control",
            self.should_continue_enhancement_iteration,
            {
                "retry": "enhance_driver_module",  # Retry enhancement with feedback
                "proceed": "more_statements_decision",  # Success - continue to next statement
                "fail": "more_statements_decision"  # Max attempts - mark as failed
            }
        )

        # Statement processing loop with attempt management
        self.builder.add_conditional_edges(
            "more_statements_decision",
            self.route_workflow_action,
            {
                "next_statement": "identify_responsible_features",  # Process next statement
                "retry_current": "categorize_execution_modules",  # Retry current statement from categorization
                "complete": "complete_processing"  # All statements processed
            }
        )

        self.builder.add_edge("complete_processing", END)

        return self.builder

    def setup_graph(self):
        """Setup and compile the Stage 2 workflow graph."""
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph

    def invoke_graph(self, data: Dict[str, Any], thread_id: str = None) -> Dict[str, Any]:
        """
        Invoke the graph with the given input data.

        Args:
            data: Dictionary containing the input data for the workflow
            thread_id: Optional thread ID for the workflow execution

        Returns:
            Dictionary containing the workflow result
        """
        thread_id = thread_id or f"thread_{uuid.uuid4()}"
        thread = {"configurable": {"thread_id": thread_id}, "recursion_limit": 20}
        return self.graph.invoke(data, config=thread)

    def should_retry_enhancement_from_validation(self, state: Stage2WorkflowState) -> str:
        """
        Determine if enhancement should retry based on validation results.

        Args:
            state: Current workflow state containing validation results

        Returns:
            "retry" for enhancement retry, "proceed" to continue pipeline
        """
        validation_passed = getattr(state, 'validation_passed', False)

        if validation_passed:
            print("✅ Module validation passed - proceeding to pipeline execution")
            return "proceed"
        else:
            print("❌ Module validation failed - retrying enhancement with validation feedback")
            return "retry"

    def should_retry_enhancement_from_execution(self, state: Stage2WorkflowState) -> str:
        """
        Determine if enhancement should retry based on execution results.

        Args:
            state: Current workflow state containing execution results

        Returns:
            "retry" for enhancement retry, "proceed" to continue pipeline
        """
        execution_success = getattr(state, 'execution_success', False)

        if execution_success:
            print("✅ Pipeline execution successful - proceeding to AI comparison")
            return "proceed"
        else:
            print("❌ Pipeline execution failed - retrying enhancement with execution feedback")
            return "retry"

    def should_continue_enhancement_iteration(self, state: Stage2WorkflowState) -> str:
        """
        Determine if enhancement iteration should continue, proceed, or fail.

        Args:
            state: Current workflow state containing iteration control results

        Returns:
            "retry" for enhancement retry, "proceed" for success, "fail" for max attempts reached
        """
        iteration_action = getattr(state, 'iteration_action', 'fail')

        if iteration_action == "proceed":
            print("✅ Enhancement successful - proceeding to next statement")
            return "proceed"
        elif iteration_action == "retry":
            print("🔄 Enhancement iteration retry - retrying with comparison feedback")
            return "retry"
        else:  # iteration_action == "fail"
            print("❌ Enhancement failed after max attempts - marking statement as failed")
            return "fail"


    def should_route_to_qmigrator_or_qbook(self, state: Stage2WorkflowState) -> str:
        """
        Determine routing based on process type.

        Args:
            state: Current workflow state containing process type

        Returns:
            "qmigrator" for object-level processing, "qbook" for statement-level processing
        """
        process_type = getattr(state, 'process_type', 'qmigrator')

        if process_type == "qmigrator":
            print("🔀 Routing to QMigrator object-level processing path")
            return "qmigrator"
        else:
            print("🔀 Routing to QBook statement-level processing path")
            return "qbook"







    def should_continue_ai_comparison(self, state: Stage2WorkflowState) -> str:
        """
        Determine if AI comparison should continue with module updates or proceed to next statement.

        This function implements the enhanced AI comparison logic:
        - Compare AI corrected output vs applied updated modules output
        - If they have similar target functionality: proceed to next statement
        - If they don't match: retry with AI feedback analysis

        Args:
            state: Current workflow state containing AI comparison results

        Returns:
            "continue" to retry module updates with feedback, "proceed" to move to next statement
        """
        ai_statements_match = getattr(state, 'ai_statements_match', False)
        current_attempt = getattr(state, 'current_attempt', 1)
        max_attempts = getattr(state, 'max_attempts', 5)

        if ai_statements_match:
            print("✅ AI statements have similar target functionality - proceeding to next statement")
            return "proceed"
        elif current_attempt <= max_attempts:
            print(f"❌ AI statements don't match - retrying with feedback (attempt {current_attempt}/{max_attempts})")
            # Note: Attempt increment happens in compare_ai_statements node
            return "continue"
        else:
            print(f"🛑 Maximum attempts reached - using latest attempt and proceeding")
            return "proceed"

    def should_continue_or_complete(self, state: Stage2WorkflowState) -> str:
        """
        Determine if we should continue processing statements, retry current statement, or complete workflow.

        Stage 2 Statement-Based Decision Logic:
            - If AI statements don't match and attempts < max: Retry current statement
            - If AI statements don't match and attempts = max: Move to next statement (use latest attempt)
            - If AI statements match: Move to next statement
            - If no more statements: Complete workflow

        Args:
            state: Current workflow state containing statement processing status

        Returns:
            "retry_current", "next_statement", or "complete"
        """
        ai_statements_match = getattr(state, 'ai_statements_match', False)
        current_attempt = getattr(state, 'current_attempt', 1)
        max_attempts = getattr(state, 'max_attempts', 5)
        approved_statements = getattr(state, 'approved_statements', [])
        current_statement_index = getattr(state, 'current_statement_index', 0)

        if ai_statements_match:
            print("✅ AI statements match - moving to next statement")
            if current_statement_index < len(approved_statements) - 1:
                print(f"📝 Processing next statement ({current_statement_index + 2}/{len(approved_statements)})")
                return "next_statement"
            else:
                print("🎉 All statements processed successfully - completing workflow")
                return "complete"
        elif current_attempt < max_attempts:
            print(f"❌ AI statements don't match - retrying current statement (attempt {current_attempt + 1}/{max_attempts})")
            return "retry_current"
        else:
            print(f"🛑 Maximum attempts reached for current statement - using latest attempt and moving to next")
            if current_statement_index < len(approved_statements) - 1:
                print(f"📝 Processing next statement ({current_statement_index + 2}/{len(approved_statements)})")
                return "next_statement"
            else:
                print("🎉 All statements processed (some with max attempts) - completing workflow")
                return "complete"

    def route_workflow_action(self, state: Stage2WorkflowState) -> str:
        """
        Route based on workflow_action set by more_statements_decision node.

        Args:
            state: Current workflow state containing workflow_action

        Returns:
            Routing decision: "next_statement", "retry_current", or "complete"
        """
        workflow_action = getattr(state, 'workflow_action', 'complete')
        current_attempt = getattr(state, 'current_attempt', 1)
        max_attempts = getattr(state, 'max_attempts', 5)
        current_statement_index = getattr(state, 'current_statement_index', 0)

        print(f"🔀 Routing based on workflow_action: {workflow_action}")
        print(f"🔢 State tracking: attempt={current_attempt}/{max_attempts}, statement={current_statement_index}")
        return workflow_action

    def save_graph_image(self, graph):
        """
        Save the workflow graph as a PNG image using Mermaid.

        Args:
            graph: Compiled LangGraph workflow
        """
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
            )

            # Save the image to a file
            graph_path = os.path.join("Conversion_Agent_Stage2", "stage2_workflow_graph.png")
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Stage 2 graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate Stage 2 graph image: {str(e)}")
            print("Continuing execution without graph visualization...")
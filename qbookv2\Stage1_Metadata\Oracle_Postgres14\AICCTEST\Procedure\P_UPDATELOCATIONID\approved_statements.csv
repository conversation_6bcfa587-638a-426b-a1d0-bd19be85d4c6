migration_name,schema_name,object_name,object_type,tgt_object_id,source_statement_number,target_statement_number,original_source_statement,original_target_statement,ai_converted_statement,original_deployment_error
Oracle_Postgres14,AICCTEST,P_UPDATELOCATIONID,Procedure,208644,3,5,"/*cursor c2
is
SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM USER_TAB_COLS UTC
WHERE UTC.COLUMN_NAME LIKE '%POSITION%'
AND UTC.TABLE_NAME NOT IN (SELECT UM.MVIEW_NAME FROM USER_MVIEWS UM
                           UNION
                           SELECT uv.view_name FROM user_views uv);*/

BEGIN

     FOR REC IN C1
     LOOP

         DBMS_OUTPUT.PUT_LINE('UPDATE '||REC.TABLE_NAME||
         ' SET '||REC.COLUMN_NAME||'='||IN_TOVAL||' WHERE '||
         REC.COLUMN_NAME||' = '||IN_FROMVAL||';');","FOR REC IN C1 LOOP
        RAISE NOTICE EXECUTE 'UPDATE % SET % = % WHERE % = %;', REC.TABLE_NAME, REC.COLUMN_NAME, IN_TOVAL, REC.COLUMN_NAME, IN_FROMVAL;","FOR REC IN C1 LOOP
       RAISE NOTICE 'UPDATE % SET % = % WHERE % = %;', REC.TABLE_NAME, REC.COLUMN_NAME, IN_TOVAL, REC.COLUMN_NAME, IN_FROMVAL;","syntax error at or near ""EXECUTE""
LINE 35:         RAISE NOTICE EXECUTE 'UPDATE % SET % = % WHERE % = %...
                              ^
"

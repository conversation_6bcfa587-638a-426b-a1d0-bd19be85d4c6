SET search_path TO AICCTEST;

CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE aicctest.P_UPDATELOCATIONID (IN_FROMVAL numeric, IN_TOVAL numeric)
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    C1 CURSOR FOR
        SELECT
            UTC.TABLE_NAME,
            UTC.COLUMN_NAME
        FROM
            aicctest.USER_TAB_COLS UTC
        WHERE
            UTC.COLUMN_NAME LIKE 'LOCATIONID'
            AND UTC.TABLE_NAME NOT IN (
                SELECT
                    UM.MVIEW_NAME
                FROM
                    aicctest.USER_MVIEWS UM);
/*
cursor c2
is
SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM USER_TAB_COLS UTC
WHERE UTC.COLUMN_NAME LIKE '%POSITION%'
AND UTC.TABLE_NAME NOT IN (SELECT UM.MVIEW_NAME FROM USER_MVIEWS UM
     UNION
     SELECT uv.view_name FROM user_views uv);
     */
    REC record;
BEGIN
    SET search_path TO AICCTEST;
FOR REC IN C1 LOOP
       RAISE NOTICE 'UPDATE % SET % = % WHERE % = %;', REC.TABLE_NAME, REC.COLUMN_NAME, IN_TOVAL, REC.COLUMN_NAME, IN_FROMVAL;
END LOOP;
/*
     DBMS_OUTPUT.PUT_LINE('UPDATINGPOSITONID');
     FOR REC IN c2
     LOOP
     DBMS_OUTPUT.PUT_LINE( 'UPDATE '||REC.TABLE_NAME||
     ' SET '||REC.COLUMN_NAME||'='''||IV_toDEPT||'-'''||'||hr.f_desigfromposition('||rec.column_name||')'||
     ' WHERE '||
     REC.COLUMN_NAME||' = '''||IV_FROMDEPT||'-'''||'||hr.f_desigfromposition('||rec.column_name||');');
     END LOOP;
     */
END;$BODY$;
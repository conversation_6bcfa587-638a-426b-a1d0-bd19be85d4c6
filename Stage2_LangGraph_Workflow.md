# Stage 2 LangGraph Workflow Documentation

## Overview
Stage 2 LangGraph workflow processes AI-corrected statements from Stage 1 through a streamlined 10-node validation and module update process. The workflow handles both object-level (qmigrator) and statement-level (qbook) processing with intelligent AI validation loops and retry mechanisms. The workflow systematically identifies and updates QMigrator Python modules to fix conversion failures at their root cause.

## Business Logic and Purpose

### Core Business Problem:
**QMigrator Conversion Accuracy**: QMigrator (the existing Oracle-to-PostgreSQL conversion tool) sometimes produces incorrect PostgreSQL code that fails during deployment. Stage 1 identifies these failures and provides AI-corrected statements, but the underlying QMigrator Python modules still contain the same conversion logic that caused the original failures.

### Stage 2 Business Objective:
**Fix the Root Cause**: Instead of just correcting individual statements, Stage 2 identifies and updates the specific Python modules in QMigrator that are responsible for conversion failures. This ensures that future conversions of similar Oracle patterns will be handled correctly automatically.

### Business Value:
1. **Automated Improvement**: QMigrator becomes smarter with each Stage 2 processing cycle
2. **Scalable Solutions**: Fixing modules benefits all future conversions, not just current ones
3. **Reduced Manual Intervention**: Less need for manual statement corrections over time
4. **Quality Assurance**: Systematic validation ensures module updates are reliable
5. **Knowledge Preservation**: AI-driven analysis captures conversion expertise in code

### Two Processing Modes:

#### QMigrator Mode (Object-Level Processing):
- **Business Case**: Process complete database objects (procedures, functions, packages) that failed conversion
- **Input**: Stage 1 metadata with approved statements and deployment errors
- **Process**: Run entire object through QMigrator to identify which specific statements and modules failed
- **Output**: Updated Python modules that fix conversion issues for the entire object

#### QBook Mode (Statement-Level Processing):
- **Business Case**: Process individual SQL statements that failed conversion
- **Input**: Single source statement and expected converted statement
- **Process**: Analyze specific statement conversion failure and update responsible modules
- **Output**: Updated Python modules that fix conversion issues for the specific statement pattern

## Current Workflow Diagram

```mermaid
flowchart TD
    A[🚀 Start Stage2 Workflow] --> B[🔀 Process Type Decision]

    B -->|qmigrator| C[📁 Post Stage1 Processing QMigrator]
    B -->|qbook| D[📝 Statement Level Processing QBook]

    C --> E[🔗 Map Feature Combinations]
    E --> F[🔍 Identify Responsible Features]
    D --> F

    F --> G[🔧 Update Responsible Modules]
    G --> H[⚙️ Apply Modules]
    H --> I[🔬 Compare AI Statements]

    I -->|Match| J[🔄 More Statements Decision]
    I -->|No Match & Retry| G

    J -->|Next Statement| F
    J -->|Retry Current| G
    J -->|Complete| K[✨ Complete Processing]

    K --> L[🏁 End]

    %% Styling for better visual distinction
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef qmigratorOnly fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef processing fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef validation fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#000
    classDef moduleWork fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef aiAnalysis fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#000

    class A,K,L startEnd
    class B,J decision
    class E qmigratorOnly
    class C,D,H processing
    class I validation
    class G moduleWork
    class F aiAnalysis
```

## Current Workflow Architecture

```mermaid
graph TB
    subgraph "🎯 Stage 2 Current Workflow Architecture"
        subgraph "📋 Input Processing"
            A1[🚀 Start Workflow]
            A2[🔀 Process Type Decision]
            A3[📁 QMigrator Path]
            A4[📝 QBook Path]
        end

        subgraph "🔍 Feature Analysis"
            B1[🔗 Map Features]
            B2[🔍 Identify Responsible Features]
        end

        subgraph "🔧 Module Enhancement"
            C1[🔧 Update Modules]
            C2[⚙️ Apply Modules]
        end

        subgraph "🔬 Validation & Control"
            D1[🔬 Compare Statements]
            D2[🔄 Statement Loop]
            D3[✨ Complete Processing]
        end
    end

    A1 --> A2
    A2 --> A3
    A2 --> A4
    A3 --> B1
    A4 --> B2
    B1 --> B2
    B2 --> C1
    C1 --> C2
    C2 --> D1
    D1 -->|❌ No Match| C1
    D1 -->|✅ Match| D2
    D2 -->|➡️ Next| B2
    D2 -->|🔄 Retry| C1
    D2 -->|🏁 Done| D3

    classDef inputGroup fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef analysisGroup fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef moduleGroup fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef validationGroup fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class A1,A2,A3,A4 inputGroup
    class B1,B2 analysisGroup
    class C1,C2 moduleGroup
    class D1,D2,D3 validationGroup
```

## Stage 1 to Stage 2 Transition

### Stage 1 Metadata Generation
Stage 1 generates metadata files that Stage 2 workflow consumes:

#### Directory Structure:
Stage 1 generates metadata files in a structured directory format organized by migration name, schema, object type, and object name. Files include approved statements, source code, target code, and final reviewed code.

#### File Contents:
- **approved_statements.csv**: Contains approved statements from Stage 1 with columns:
  - `migration_name`, `schema_name`, `object_name`, `object_type`, `tgt_object_id`
  - `source_statement_number`, `target_statement_number`
  - `original_source_statement`, `original_target_statement`, `ai_converted_statement`
  - `original_deployment_error`
- **source_code.sql**: Complete original source code
- **target_code.sql**: Complete target code
- **final_code.sql**: Final reviewed and approved code
- **Conversion_Files.xlsx**: Project-specific conversion files from fileshare
- **Pattern-based files**: All files ending with `_Package_Extraction.csv` (e.g., HR_Package_Extraction.csv, FINANCE_Package_Extraction.csv)

#### File Copying Strategy:
- **Single File Copy**: `copy_project_file()` function for specific files like `Conversion_Files.xlsx`
- **Pattern-Based Copy**: `copy_files_by_pattern()` function for files ending with specific patterns
- **Graceful Handling**: No errors raised if files don't exist - informational logging only
- **Multiple File Support**: Pattern matching can copy multiple files (e.g., HR_Package_Extraction.csv, FINANCE_Package_Extraction.csv)

#### Path Configuration:
- **Local Environment**: Uses `Config.Qbook_Local_Path` for QBook files, `Config.Local_Path` for project files
- **Cloud Environment**: Uses `Config.Qbook_Path` for QBook files, `Config.Cloud_Path` for project files
- **Dynamic Project ID**: Uses `PRJ{project_id}SRC` format for fileshare paths
- **Source Path**: `PRJ{project_id}SRC/{run_no}/Source/{schema_name}/`
- **Target Path**: `Stage1_Metadata/{migration_name}/{schema_name}/`

## Current Workflow Implementation - Node Details

### 1. 🔀 Process Type Decision

#### Business Logic:
- **Purpose**: Route processing based on the scope and nature of conversion failures
- **Business Decision Factors**:
  - **Object-Level Failures**: When entire database objects have multiple related conversion issues
  - **Statement-Level Failures**: When specific SQL statements need targeted, isolated fixes
- **Resource Optimization**: Different processing paths optimize for different failure patterns and complexity levels

#### Technical Implementation:
- **Decision Logic**: Routes workflow based on `process_type` parameter
- **Input Parameter**: `process_type` ("qmigrator" or "qbook")
- **QMigrator Path**: Routes to comprehensive object-level processing with Stage 1 metadata integration
- **QBook Path**: Routes to focused statement-level processing with direct statement input

#### Business Impact:
- **QMigrator Mode**: Processes complete objects, fixes multiple related conversion issues in one cycle
- **QBook Mode**: Provides quick, targeted fixes for isolated conversion problems
- **Efficiency**: Ensures appropriate resource allocation based on problem scope

#### Routing Logic:
```python
if state.process_type == "qmigrator":
    return "post_stage1_processing_qmigrator"
else:
    return "statement_level_processing_qbook"
```

### 2. 📁 Post Stage1 Processing - QMigrator

#### Business Logic:
- **Purpose**: Establish the baseline for what QMigrator currently produces vs. what it should produce
- **Business Problem**: Stage 1 identified conversion failures, but we need to understand exactly which QMigrator features are involved
- **Why This Matters**: Without knowing which specific Python modules processed each statement, we cannot target our fixes effectively
- **Business Value**: Creates a comprehensive mapping between failed statements and the QMigrator features that should have handled them correctly

#### Business Process Flow:
1. **Load Stage 1 Results**: Retrieve approved statements that represent the "correct" conversions
2. **Reproduce QMigrator Behavior**: Run the same object through QMigrator to see what it actually produces
3. **Feature Mapping**: Identify which Python modules were involved in processing each statement
4. **Gap Analysis**: Compare what QMigrator produced vs. what Stage 1 determined was correct

#### Technical Implementation:
- **Input Requirements**:
  - `process_type="qmigrator"`
  - `migration_name`: Migration identifier
  - `schema_name`: Database schema name
  - `object_name`: Database object name
  - `objecttype`: Object type (procedure, function, package)
  - `cloud_category`: Environment type for path resolution

#### Stage 1 Metadata Integration:
- **Metadata Directory Structure**: `Stage1_Metadata/{migration_name}/{schema_name}/{objecttype}/{object_name}/`
- **Core Files Loaded**:
  - **`approved_statements.csv`**: Contains Stage 1 approved statements with columns:
    - `migration_name`, `schema_name`, `object_name`, `object_type`, `tgt_object_id`
    - `source_statement_number`, `target_statement_number`
    - `original_source_statement`, `original_target_statement`, `ai_converted_statement`
    - `original_deployment_error`
  - **`source_code.sql`**: Complete original Oracle source code
  - **`target_code.sql`**: Complete target PostgreSQL code
  - **`final_code.sql`**: Final reviewed and approved code

#### Schema-Level Resource Files:
- **`Conversion_Files.xlsx`**: Project-specific conversion configuration files
- **Pattern-Based Files**: All files ending with `_Package_Extraction.csv` (e.g., HR_Package_Extraction.csv, FINANCE_Package_Extraction.csv)
- **Dynamic Loading**: Uses pattern matching to copy multiple related files

#### QMigrator Object-Level Conversion Process:
- **Integration**: Leverages existing QMigrator object-level conversion functionality
- **Input Parameters**: Complete object context including approved statements and source code
- **Processing**: Runs entire object through QMigrator to generate statement-by-statement feature analysis
- **Output**: Structured data containing statement information and associated feature modules with conversion results

#### Excel File Creation and Management:
- **Filename Convention**: `{schema_name}_{object_name}.xlsx`
- **Sheet Structure**:
  - **"Source_Code"**: Complete Oracle source code for reference
  - **"Available_Features"**: QMigrator conversion results with detailed feature analysis
- **Data Storage**: Comprehensive QMigrator output stored for subsequent processing steps
- **Path Management**: Uses cloud_category for dynamic path resolution (Local vs Cloud environments)

### 3. 📝 Statement Level Processing - QBook

#### Business Logic:
- **Purpose**: Handle direct statement-level processing for targeted conversion fixes
- **Business Case**: When specific SQL statements need isolated analysis and module updates
- **Efficiency**: Bypasses object-level processing for focused statement analysis

#### Technical Implementation:
- **Input Requirements**:
  - `process_type="qbook"`
  - `migration_name`: Migration identifier
  - `schema_name`: Database schema name
  - `object_name`: Database object name
  - `source_statement`: Original Oracle statement
  - `converted_statement`: Expected PostgreSQL conversion

#### Processing Characteristics:
- **Direct Statement Processing**: No file reading required - statements provided directly in request
- **QMigrator Statement Level Conversion**: Leverages existing QMigrator statement-level analysis
- **Output**: Statement-level analysis for single statement with feature information and conversion details

### 4. 🔗 Map Feature Combinations (QMigrator Only)

#### Business Logic:
- **Purpose**: Create the critical link between conversion failures and the tools that should fix them
- **Business Problem**: We have two separate datasets that need correlation:
  - **Stage 1 Data**: Statements that failed and their correct conversions
  - **QMigrator Data**: Features that were used to process each statement
- **Business Need**: Combine these datasets to understand which features failed to produce the correct output
- **Strategic Value**: This mapping enables targeted fixes rather than guessing which modules need updates

#### Why This Mapping is Critical:
- **Precision**: Only update modules that are actually responsible for failures
- **Efficiency**: Avoid unnecessary changes to working modules
- **Traceability**: Maintain clear links between problems and solutions
- **Quality**: Ensure fixes address real conversion issues, not imaginary ones
- **Cost Effectiveness**: Focus development effort on actual problem areas

#### Business Process Flow:
1. **Data Correlation**: Match failed statements with the QMigrator features that processed them
2. **Gap Identification**: Identify where QMigrator features produced incorrect output
3. **Scope Definition**: Determine which Python modules need enhancement for each conversion failure
4. **Priority Setting**: Focus on statements where both failure data and feature data are available

#### Technical Implementation:
- **Challenge**: Map approved statements from Stage 1 with available features from QMigrator object-level conversion
- **Goal**: Create combined dataset that includes both approved statement data and QMigrator feature analysis
- **Processing**: Combines data based on Source_Statement_Number matching for precise correlation

#### Mapping Process Implementation:
- **Input Sources**:
  - `approved_statements`: Approved statements from Stage 1 metadata with conversion failures
  - `available_features`: QMigrator object-level conversion output with feature analysis
- **Matching Logic**:
  - Match based on `Source_Statement_Number` field for precise statement correlation
  - Only create combined entries when statement numbers match exactly
  - No entries created for unmatched approved statements to maintain data integrity
- **Data Combination**: Creates comprehensive combined dataset entries with all required fields for subsequent analysis

#### Validation and Quality Control:
- **Statement Number Matching**: Only processes statements with exact matching numbers
- **Data Integrity**: Ensures all required fields are present in combined dataset
- **Missing Data Handling**: Graceful handling of missing or mismatched data with appropriate logging
- **Count Validation**: Tracks number of successfully combined entries for audit purposes

### 5. 🔍 Identify Responsible Features

#### Business Logic:
- **Purpose**: Pinpoint exactly which Python modules in QMigrator are causing conversion failures
- **Business Problem**: QMigrator has hundreds of Python modules, but only a few are responsible for each specific conversion failure
- **Business Value**: Enables surgical fixes instead of broad changes that could break working functionality
- **Cost Savings**: Reduces development time by focusing on actual problem areas rather than guessing

#### Why This Analysis is Critical:
- **Root Cause Analysis**: Identifies the specific code that needs fixing
- **Risk Mitigation**: Avoids changing modules that are working correctly
- **Quality Assurance**: Ensures fixes target the actual source of conversion problems
- **Scalability**: Creates a systematic approach to improving QMigrator over time
- **Precision**: Focuses effort on modules that will actually solve the conversion problems

#### Statement-by-Statement Processing Model:
- **Processing Approach**: Processes **ONE statement at a time** through the entire workflow
- **Business Rationale**: Each statement may have different responsible modules, requiring focused analysis
- **Current Statement Tracking**: Uses `current_statement_index` from state to track progress (0-based indexing)
- **Loop Control**: After processing each statement, workflow determines next action:
  - **Success**: Move to next statement (increment index, reset attempts)
  - **Retry**: Retry current statement (increment attempt counter)
  - **Complete**: All statements processed successfully

#### Enhanced AI Analysis with Structured Output:
- **Purpose**: Identify specific Python modules causing Oracle-to-PostgreSQL conversion failures
- **Key Understanding**:
  - **Oracle ≠ PostgreSQL**: Always different (different database languages)
  - **Problem**: Expected PostgreSQL ≠ Actual PostgreSQL (conversion failed)
  - **Goal**: Find which Python modules should have converted Oracle syntax but failed

#### Comprehensive AI Analysis Process:
- **Input Data Context**:
  - `original_source_statement`: Original Oracle code requiring conversion
  - `ai_converted_statement`: Expected PostgreSQL (what QMigrator should have produced)
  - `original_target_statement`: Actual PostgreSQL (what QMigrator actually produced)
  - `original_deployment_error`: Error message from deployment indicating failure
  - `available_features`: List of Python modules that should handle conversion
  - `decrypted_modules`: Actual Python module code for detailed analysis
  - `keyword_mapping`: Oracle keyword to module mapping from CSV configuration
  - `validation_feedback`: Feedback from previous validation failures (for retry attempts)

#### Structured Output Implementation:
- **AI Integration**: Uses `with_structured_output(ResponsibleFeaturesAnalysisOutput)` for reliable AI responses
- **Pydantic Model Structure**:
  ```python
  class ResponsibleFeature(BaseModel):
      feature_name: str              # Name of the responsible feature
      module_path: str               # Path to the Python module
      responsibility_reason: str     # Why this module is responsible
      error_impact: str             # How this module's failure impacts conversion
      keywords_matched: List[str]   # Oracle keywords that should trigger this module
      confidence_score: float       # AI confidence in this identification

  class ResponsibleFeaturesAnalysisOutput(BaseModel):
      responsible_features: List[ResponsibleFeature]  # List of identified modules
      analysis_summary: str                          # Detailed explanation of analysis
  ```

#### Advanced Analysis Logic:
- **Comparison Analysis**: Expected (AI corrected) vs Actual (QMigrator output) for gap identification
- **Keyword Matching**: Oracle keywords in source that should trigger specific modules
- **Error Correlation**: Deployment errors pointing to specific module failures
- **Module Validation**: Which modules should have converted Oracle syntax but failed
- **Feedback Integration**: Uses validation feedback from previous attempts to improve identification accuracy

#### Module Decryption and Code Analysis:
- **Encrypted Module Storage**: Python modules in qbookv2/Conversion_Modules are encrypted for security
- **Decryption Process**: Modules are decrypted before AI analysis using existing decryption utilities
- **Module Content Analysis**: AI analyzes actual Python code to understand conversion logic and identify failures
- **Path Resolution**: Uses cloud_category from state for correct path selection (Local vs Cloud environments)

#### Keyword Mapping Integration:
- **CSV Configuration**: Oracle_Postgres.csv contains keyword-to-module mapping for systematic identification
- **Dynamic Loading**: Loads mapping based on migration_name and cloud_category for environment-specific configuration
- **Keyword Correlation**: Maps Oracle keywords in source to responsible modules for targeted analysis
- **Fallback Handling**: Graceful handling when CSV file is not found with appropriate logging


### 6. 🔧 Update Responsible Modules (Driver Module Approach)

#### Business Logic:
- **Purpose**: Fix the root cause of conversion failures by updating the actual QMigrator code using an intelligent driver module approach
- **Business Impact**: This is where the real value is delivered - making QMigrator smarter and more accurate through holistic module enhancement
- **Long-term Value**: Each module update improves QMigrator for all future conversions, not just the current one
- **ROI Justification**: Investment in module updates pays dividends across thousands of future conversions

#### Why Driver Module Approach is Superior:
- **Holistic Enhancement**: AI sees all responsible modules together, understanding their interactions and dependencies
- **Intelligent Selection**: Only modules with actual functional changes get updated, avoiding unnecessary modifications
- **Context Awareness**: AI can make better decisions when it sees the complete picture of all modules involved
- **Reduced Errors**: Eliminates the problem of updating wrong modules by providing full context to AI
- **Efficient Processing**: Single AI enhancement call for multiple modules instead of individual processing

#### Revolutionary Driver Module Process:
1. **Module Combination**: All responsible modules are combined into a single "driver module" with clear boundary markers
2. **Holistic AI Analysis**: AI analyzes the complete combined module with full context of all interactions
3. **Intelligent Enhancement**: AI makes targeted improvements while understanding module relationships
4. **Smart Decomposition**: Enhanced combined module is decomposed back into individual modules
5. **Selective Updates**: Only modules with functional changes are saved as attempt files, unchanged modules use originals

#### Business Process Flow:
1. **Code Analysis**: AI examines all responsible module codes together to understand complete conversion context
2. **Holistic Solution Design**: AI designs enhancements considering all module interactions and dependencies
3. **Intelligent Implementation**: AI generates updated Python code with full awareness of module relationships
4. **Selective Deployment**: Only functionally changed modules are updated, preserving working modules

#### Driver Module Technical Implementation:
- **Goal**: Update Python conversion modules using intelligent driver module approach for superior AI enhancement
- **Combined Module Processing**: All responsible modules are combined into a single driver module with boundary markers
- **Holistic AI Enhancement**: AI analyzes and enhances the complete combined module with full context
- **Intelligent Decomposition**: Enhanced combined module is decomposed back with functional change detection
- **Selective Module Updates**: Only modules with actual functional changes are saved as attempt files

#### Driver Module Architecture:
```python
# === MODULE: nvl START ===
import re
def nvl(data, sch):
    # Original NVL conversion logic
    return data
# === MODULE: nvl END ===

# === MODULE: join START ===
import re
def join(data, sch):
    # Original JOIN conversion logic
    return data
# === MODULE: join END ===

# === MODULE: order_by START ===
import re
def order_by(data, sch):
    # Original ORDER BY conversion logic
    return data
# === MODULE: order_by END ===
```

#### Driver Module Enhancement Workflow:

##### Phase 1: Module Combination
- **Input**: List of responsible modules `[(feature_name, module_path, module_code), ...]`
- **Process**: Combine all modules into single driver module with boundary markers
- **Output**: Combined module code with clear module separation markers
- **Example**:
  ```python
  # === MODULE: nvl START ===
  [nvl module code]
  # === MODULE: nvl END ===
  # === MODULE: join START ===
  [join module code]
  # === MODULE: join END ===
  ```

##### Phase 2: Holistic AI Enhancement
- **Input**: Combined module code + AI context (source, target, expected output)
- **Process**: AI analyzes complete combined module and makes intelligent enhancements
- **AI Advantage**: Sees all modules together, understands interactions and dependencies
- **Output**: Enhanced combined module code with AI improvements

##### Phase 3: Intelligent Decomposition
- **Input**: Original modules + Enhanced combined module + Module boundaries
- **Process**: Extract individual modules from enhanced combined code
- **Change Detection**: Compare original vs enhanced for each module using functional analysis
- **Output**: List of modules with change status (changed/unchanged)

##### Phase 4: Selective Module Saving
- **Changed Modules**: Save as attempt files (e.g., `join_attempt_1.py`)
- **Unchanged Modules**: Continue using original files (e.g., `nvl.py`)
- **Functional Change Detection**: Ignores comment changes, focuses on actual code logic
- **File Management**: Only creates attempt files for modules with real functional changes

#### Enhanced Module Reading and Decryption:
- **Encrypted Module Storage**: Python modules stored in encrypted format in `qbookv2/Conversion_Modules/{migration_name}/{module_path}`
- **Dynamic Path Construction**: Uses responsible features `(feature_name, module_path)` from previous validation
- **Example Path**: `qbookv2/Conversion_Modules/Oracle_Postgres14/Common/Statement/Pre/raisenotice.py`
- **Decryption Process**: Modules are decrypted before AI analysis using existing decryption utilities
- **Cloud Category Support**: Uses `cloud_category` from state for correct path selection (Local vs Cloud)
- **Module Information Storage**: Collects keywords, paths, and original code for complete context

#### Module Path Resolution Implementation:
- **Input**: Responsible features as tuples `(feature_name, module_path)` from validation node
- **Path Construction**:
  ```python
  # Example: ('Raisenotice', 'Common/Statement/Pre/raisenotice.py')
  full_module_path = os.path.join(
      base_path,
      'Conversion_Modules',
      migration_name,
      module_path  # Already contains full relative path including .py
  )
  ```
- **Base Path Selection**:
  - **Local Environment**: `Config.Qbook_Local_Path`
  - **Cloud Environment**: `Config.Qbook_Path`

#### Driver Module AI Enhancement Process:
- **Context Preparation**: Builds comprehensive analysis context for driver module AI enhancement
- **Input Data for AI**:
  - `combined_module_code`: All responsible modules combined with boundary markers
  - `original_source_statement`: Oracle source code that needs conversion
  - `ai_converted_statement`: Expected PostgreSQL output (what should be produced)
  - `original_target_statement`: Actual PostgreSQL output (what QMigrator currently produces)
  - `original_deployment_error`: Error message indicating conversion failure
  - `conversion_context`: Additional context about the conversion scenario
  - `combined_keywords`: Keywords from all responsible modules
  - `db_terms`: Database-specific terminology for prompts
  - `ai_comparison_feedback`: Feedback from previous attempts for refinement

#### Structured AI Enhancement:
- **Uses**: `with_structured_output(ModuleEnhancementOutput)` for reliable AI responses
- **Pydantic Model**:
  ```python
  class ModuleEnhancementOutput(BaseModel):
      enhanced_code: str                # Updated Python module code
      enhancement_summary: str          # Explanation of changes made
      changes_made: List[str]           # List of specific modifications
      preserved_functionality: bool     # Whether existing functionality is preserved
  ```

#### Advanced Driver Module AI Enhancement Logic:
- **Holistic Problem Analysis**: AI analyzes why current modules fail to convert Oracle to expected PostgreSQL with full context
- **Intelligent Code Enhancement**: Modifies combined module considering all module interactions and dependencies
- **Context-Aware Solutions**: Creates fixes that work with module relationships and avoid conflicts
- **Selective Functionality Preservation**: Ensures existing module functionality remains intact while enhancing specific areas
- **Comprehensive Error Handling**: Adds robust error handling considering all module interactions
- **Boundary Preservation**: Maintains module boundary markers exactly for proper decomposition

#### Module Saving with Filename Consistency:
- **Filename Preservation**: Maintains original filename case and format
- **Example**: Reading `raisenotice.py` → Saving `raisenotice_attempt_1.py` (not `Raisenotice_attempt_1.py`)
- **Attempt Tracking**: Adds `_attempt_{attempt_number}` suffix for version tracking
- **Path Extraction**: Uses `os.path.basename()` and `os.path.splitext()` to extract original filename
- **Storage Location**: Saves to `Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/feature_modules/{statement_number}/`

#### Driver Module Excel Logging System:

##### Main Sheet (Individual Module Results):
- **Changed Modules**: Modules that were functionally enhanced
  - `statement_number`: Which statement was processed
  - `feature_name`: Name of the enhanced module
  - `module_path`: Original module path
  - `keywords`: Keywords associated with the module
  - `updated_module_path`: Path to attempt file (e.g., `join_attempt_1.py`)
  - `original_code`: Original module code
  - `updated_code`: Enhanced module code
  - `code_changed`: True (indicates functional changes)
  - `enhancement_analysis`: "Driver module approach - functional changes detected"

- **Unchanged Modules**: Modules that remained functionally unchanged
  - `updated_module_path`: "Using original module"
  - `code_changed`: False (indicates no functional changes)
  - `enhancement_analysis`: "Driver module approach - no functional changes"

##### driver_modules Sheet (Combined Module Tracking):
- **Statement_Number**: Which statement was processed
- **Attempt_Number**: Which attempt this was
- **Timestamp**: When the processing occurred
- **Responsible_Modules**: List of all modules involved (e.g., "nvl, join, order_by")
- **Total_Modules**: Count of modules processed
- **Changed_Modules**: List of modules that were functionally changed (e.g., "join")
- **Unchanged_Modules**: List of modules that remained unchanged (e.g., "nvl, order_by")
- **Changed_Count**: Number of changed modules
- **Unchanged_Count**: Number of unchanged modules
- **Enhancement_Status**: SUCCESS/FAILED
- **Combined_Module_Code**: Original combined module code with all modules and boundary markers
- **Enhanced_Combined_Code**: AI-enhanced combined module code with improvements
- **Analysis**: Summary of enhancement results (e.g., "Driver module approach: 1 modules enhanced, 2 modules unchanged. Enhanced modules: join")

#### Comprehensive Error Handling:
- **Module Reading Failures**: Handles cases where modules cannot be read or decrypted
- **AI Enhancement Failures**: Graceful handling of AI analysis errors with complete context
- **Code Validation**: Validation of AI-generated combined module structure and boundary markers
- **Retry Mechanism**: Supports retry attempts with enhanced AI context and feedback
- **Fallback Handling**: Continues processing even if some modules fail to update
- **State Validation**: Comprehensive validation of all input data and state variables

### 7. ⚙️ Apply Modules

#### Business Logic:
- **Purpose**: Apply updated Python modules to transform source statements and generate new converted statements
- **Business Problem**: After updating modules, we need to test if they actually produce the correct conversion output
- **Why This Matters**: Module updates are only valuable if they generate the expected converted statements
- **Business Value**: Validates that module improvements work correctly before deploying them to production

#### Technical Implementation:
- **Input Context**:
  - `current_statement_index`: Index of statement being processed (0-based)
  - `approved_statements`: List of statements with source and target information
  - `responsible_features`: Features identified for current statement
  - `current_attempt`: Current attempt number for retry logic
- **Output Results**:
  - `updated_ai_converted_statement`: New statement generated by applying updated modules
  - `applied_features_log`: Log of which features were applied and their results

#### Module Application Process:
1. **Get Current Statement**: Extract source statement using `current_statement_index`
2. **Load Updated Modules**: Read updated Python modules from attempt-based storage paths
3. **Apply Module Transformation**: Execute Python modules using `exec()` to transform source statement
4. **Generate Output**: Produce `updated_ai_converted_statement` from module application
5. **Log Results**: Track which features were applied and their transformation results

#### Module Application Logic:
- **Start with**: Source statement from `approved_statements[current_statement_index]`
- **Apply statement-level features**:
  - Use **updated modules** (e.g., `join_attempt_1.py`) when available for current attempt
  - Use **original modules** (e.g., `join.py`) for features not updated in current attempt
  - Execute modules using direct Python `exec()` for statement transformation
- **Generate**: `updated_ai_converted_statement` as final output

### 8. 🔬 Compare AI Statements

#### Business Logic:
- **Purpose**: Validate that updated Python modules produce the same target functionality as AI-corrected statements
- **Business Problem**: Module updates should generate equivalent output to Stage 1 AI corrections
- **Why This Matters**: If modules don't produce equivalent results, they need further refinement
- **Business Value**: Ensures module improvements actually solve the conversion problems they were designed to fix

#### Technical Implementation:
- **Input Context**:
  - `ai_converted_statement`: Original AI-corrected statement from Stage 1
  - `updated_ai_converted_statement`: New statement generated by applying updated modules
  - `current_attempt`: Current attempt number for retry logic
  - `max_attempts`: Maximum attempts allowed (default: 5)
- **Output Results**:
  - `ai_statements_match`: Boolean indicating if statements have similar target functionality
  - `ai_comparison_feedback`: Detailed analysis of differences when statements don't match

#### AI Comparison Process:
1. **Load AI Comparison Prompt**: Use structured AI analysis for statement comparison
2. **Compare Functionality**: AI analyzes if both statements achieve the same target functionality
3. **Generate Feedback**: When statements don't match, provide detailed analysis of differences
4. **Set Match Status**: Update `ai_statements_match` based on AI analysis results
5. **Route Decision**: Determine next workflow action based on match status and attempt count

#### AI Comparison Logic:
- **Compare**: `ai_converted_statement` (Stage 1 AI correction) vs `updated_ai_converted_statement` (module output)
- **AI Analysis**: Determines if statements have similar target functionality (not exact text match)
- **Account for**: Acceptable variations in formatting, syntax, and implementation approach
- **Match**: Statements achieve same target functionality → proceed to next statement
- **No Match**: Statements have different functionality → retry with feedback or move to next statement if max attempts reached

### 9. 🔄 More Statements Decision

#### Business Logic:
- **Purpose**: Manage statement-by-statement processing loop with intelligent retry and completion logic
- **Business Problem**: Need to process all statements while handling failures gracefully
- **Why This Matters**: Ensures comprehensive processing while preventing infinite loops
- **Business Value**: Maximizes successful statement processing while maintaining workflow efficiency

#### Technical Implementation:
- **Input Context**:
  - `ai_statements_match`: Result from AI statement comparison
  - `current_statement_index`: Index of current statement being processed (0-based)
  - `current_attempt`: Current attempt number for current statement
  - `max_attempts`: Maximum attempts allowed per statement (default: 5)
  - `approved_statements`: List of all statements to process
- **Output Results**:
  - `workflow_action`: Next action for workflow routing ("next_statement", "retry_current", "complete")
  - Updated state variables for next processing cycle

#### Decision Process:
1. **Evaluate Current Status**: Check AI statement comparison results and attempt count
2. **Determine Next Action**: Based on success/failure and remaining statements/attempts
3. **Update State Variables**: Prepare state for next processing cycle
4. **Set Workflow Action**: Route to appropriate next workflow step
5. **Log Progress**: Track statement processing progress and completion status

#### Decision Logic:
- **Success (`ai_statements_match = True`)**:
  - If more statements exist: Move to next statement (increment `current_statement_index`, reset `current_attempt` to 1)
  - If all statements processed: Complete workflow
- **Failure (`ai_statements_match = False`)**:
  - If `current_attempt < max_attempts`: Retry current statement (increment `current_attempt`)
  - If `current_attempt = max_attempts`: Move to next statement (use latest attempt as final)
- **Workflow Actions**:
  - `"next_statement"`: Move to next statement processing
  - `"retry_current"`: Retry current statement with incremented attempt
  - `"complete"`: All statements processed successfully

### 10. ✨ Complete Processing

#### Business Logic:
- **Purpose**: Finalize Stage 2 workflow and prepare results for production deployment
- **Business Problem**: Need to consolidate successful module updates and provide comprehensive processing summary
- **Why This Matters**: Ensures all improvements are properly documented and ready for deployment
- **Business Value**: Provides clear audit trail and deployment-ready module updates

#### Technical Implementation:
- **Input Context**:
  - All processing state variables from completed workflow
  - Updated modules from successful attempts
  - Processing logs and Excel tracking data
- **Output Results**:
  - Final processing summary with success/failure statistics
  - Consolidated module update recommendations
  - Workflow completion status

#### Final Processing Steps:
1. **Consolidate Results**: Gather all successful module updates and processing outcomes
2. **Generate Summary**: Create comprehensive report of workflow execution
3. **Update Database**: Store final results in appropriate database tables
4. **Clean Resources**: Remove temporary files and reset state variables
5. **Mark Complete**: Set workflow status to completed

#### Final Actions:
- **Database Updates**: Store successful module updates in `stage2_conversion_modules` table
- **Summary Generation**: Create detailed processing report with statistics and outcomes
- **Resource Cleanup**: Remove temporary files and clear workflow state
- **Completion Marking**: Set workflow status to completed for audit purposes





## Technical Implementation Details

### Advanced State Management
- **Statement Tracking**: `current_statement_index` tracks which statement is being processed (0-based indexing)
- **Attempt Tracking**: `current_attempt` tracks retry attempts per statement (1-5 attempts)
- **Responsible Features**: Stored as `List[tuple]` containing `(feature_name, module_path)` for precise module identification
- **Cloud Category**: Retrieved from state for dynamic path resolution between local and cloud environments
- **Excel Logging**: Comprehensive tracking in multiple sheets for complete audit trail

### AI Integration Architecture
- **Structured Output**: Uses `with_structured_output()` for reliable AI responses with type safety
- **Pydantic Models**: Type-safe data structures for AI analysis results ensuring data integrity
- **Prompt Engineering**: Detailed prompts with examples for consistent AI behavior across all nodes
- **Error Handling**: Graceful fallback when AI analysis fails with appropriate retry mechanisms
- **Feedback Loops**: Comprehensive feedback system for continuous improvement

### Module Management System
- **Dynamic Paths**: Uses `cloud_category` from state for local vs cloud path selection
- **Keyword Mapping**: Loads Oracle-to-module mapping from CSV files for systematic identification
- **Module Decryption**: Decrypts Python modules for AI analysis using existing security utilities
- **Path Construction**: Builds full module paths using migration name and cloud category for environment flexibility
- **Version Control**: Attempt-based module versioning for tracking changes and rollback capability

### Excel Integration and Logging
- **Statement-by-Statement Logging**: One row per statement processing with complete context
- **Comprehensive Analysis**: Includes detailed AI analysis summary for each processing step
- **Tuple Format**: Preserves `(name, path)` format for responsible features maintaining data relationships
- **Timestamp Tracking**: Records processing time and attempt number for audit and performance analysis
- **Multi-Sheet Architecture**: Separate sheets for different processing stages and data types

### Workflow Control

#### Statement-by-Statement Processing:
- **Processing Model**: Processes **ONE statement at a time** through the entire workflow
- **Current Statement Tracking**: Uses `current_statement_index` from state to track progress
- **Sequential Processing**: Each statement goes through complete validation cycle before moving to next

#### State Management Implementation:
- **Key State Fields**:
  - `current_statement_index`: Tracks which statement is being processed (0-based index)
  - `current_attempt`: Tracks retry attempts per statement (1-5)
  - `responsible_features_data`: Stores detailed data for current statement processing
  - `validation_feedback`: Stores feedback from validation failures for retry attempts
  - `cloud_category`: Used for dynamic path resolution (Local vs Cloud)

#### Retry Logic and Attempt Management:
- **Per-Statement Attempts**: Up to 5 attempts per statement with proper state management
- **Attempt Tracking**: `current_attempt` increments with each retry
- **Feedback Integration**: Validation failures provide feedback for next identification attempt
- **State Persistence**: All attempt data stored in Excel for tracking and analysis

#### Loop Control Implementation:
- **Conditional Routing**: Uses `should_continue_responsible_features` and `should_continue_or_complete` functions
- **Retry Loops**: AI comparison failure loops back to module updates with feedback
- **Statement Progression**: `more_statements_decision` manages statement iteration
- **Completion Logic**: Handles both success and max attempt scenarios

#### Workflow State Transitions:
```python
# Successful validation
identify_responsible_features → features_valid_decision → update_responsible_modules

# Validation failure with feedback
identify_responsible_features → features_valid_decision → identify_responsible_features (with feedback)

# Statement completion
current_statement_complete → more_statements_decision → next_statement OR complete
```

#### Error Handling and Recovery:
- **Infinite Loop Prevention**: Recursion limit prevents endless retry loops
- **Graceful Degradation**: Handles AI analysis failures with appropriate fallbacks
- **State Cleanup**: Clears validation feedback after successful use
- **Progress Tracking**: Comprehensive logging of all state transitions and decisions

### Prompt Engineering
- **Structured Prompts**: Detailed prompts in `prompts/responsible_features_identification_prompt.py`
- **Clear Examples**: Multiple examples showing success and failure scenarios
- **Terminology Consistency**: Uses "Expected vs Actual" terminology throughout
- **Comprehensive Analysis**: Prompts AI to provide detailed analysis summary covering all responsible features
- **Output Format**: JSON structure matching Pydantic models for reliable parsing

### AI Analysis Features
- **Conversion Failure Detection**: Identifies when Expected ≠ Actual PostgreSQL output
- **Keyword Mapping**: Maps Oracle keywords to responsible Python modules
- **Error Correlation**: Analyzes deployment errors to identify specific module failures
- **Confidence Scoring**: AI provides confidence scores for each identified module
- **Multiple Module Support**: Handles cases with multiple responsible modules per statement
- **Comprehensive Reasoning**: Detailed analysis summary explaining why each module is responsible

## Current Implementation Status

### ✅ Fully Implemented and Tested Nodes:

#### 1. 🔀 Process Type Decision ✅
- **Status**: Production Ready
- **Functionality**: Intelligent routing based on process_type parameter
- **Features**: QMigrator vs QBook routing logic, environment-aware path selection

#### 2. 📁 Post Stage1 Processing - QMigrator ✅
- **Status**: Production Ready
- **Functionality**: Stage 1 metadata integration and QMigrator object-level conversion
- **Features**: File reading, QMigrator integration, Excel file creation, cloud category support, pattern-based file copying

#### 3. 📝 Statement Level Processing - QBook ✅
- **Status**: Production Ready
- **Functionality**: Direct statement-level processing for targeted conversion fixes
- **Features**: Statement-level QMigrator integration, direct input processing

#### 4. 🔗 Map Feature Combinations ✅
- **Status**: Production Ready
- **Functionality**: Combines approved statements with available features for correlation (QMigrator path only)
- **Features**: Statement number matching, combined dataset creation, Excel integration, data integrity validation

#### 5. 🔍 Identify Responsible Features ✅
- **Status**: Production Ready
- **Functionality**: AI-driven identification of responsible Python modules
- **Features**: Structured AI analysis, module decryption, keyword mapping, Excel logging, feedback integration, confidence scoring

#### 6. 🔧 Update Responsible Modules (Driver Module Approach) ✅
- **Status**: Production Ready with Revolutionary Driver Module Enhancement
- **Functionality**: AI-driven enhancement of Python conversion modules using intelligent driver module approach
- **Features**: Module combination, holistic AI enhancement, intelligent decomposition, selective updates, comprehensive Excel tracking
- **Advanced Capabilities**:
  - **Holistic Processing**: All responsible modules processed together for better AI context
  - **Intelligent Selection**: Only functionally changed modules get updated
  - **Dual Excel Tracking**: Main sheet for individual results + driver_modules sheet for combined tracking
  - **Functional Change Detection**: Ignores comment changes, focuses on actual code logic
  - **Complete Audit Trail**: Both original and enhanced combined code stored for analysis

#### 7. ⚙️ Apply Modules ✅
- **Status**: Production Ready
- **Functionality**: Apply updated modules to transform source statements and generate new converted statements
- **Features**: Module application using exec(), attempt-based module selection, statement transformation, result logging

#### 8. 🔬 Compare AI Statements ✅
- **Status**: Production Ready
- **Functionality**: AI comparison of original vs updated AI converted statements
- **Features**: Structured AI comparison, functionality equivalence validation, detailed feedback generation, match analysis

#### 9. 🔄 More Statements Decision ✅
- **Status**: Production Ready
- **Functionality**: Manage statement-by-statement processing loop with intelligent retry and completion logic
- **Features**: Statement iteration, attempt management, workflow action routing, state variable updates, progress tracking

#### 10. ✨ Complete Processing ✅
- **Status**: Production Ready
- **Functionality**: Finalize workflow and generate comprehensive processing summary
- **Features**: Result consolidation, summary generation, resource cleanup, completion marking, audit trail

### Implementation Architecture:
- **Current Status**: All 10 Stage 2 workflow nodes are fully implemented, tested, and production-ready
- **Architecture**: All nodes follow consistent patterns for Excel logging, state management, and error handling
- **Testing**: Each node has been tested with real data and error scenarios
- **Workflow Integration**: Streamlined end-to-end workflow with intelligent retry mechanisms
- **Quality Assurance**: Comprehensive error handling and graceful degradation throughout

### Revolutionary Driver Module Approach Implementation:

#### Key Advantages Over Traditional Module Enhancement:

##### 1. **Holistic AI Context**:
- **Traditional Approach**: AI processes each module individually without understanding relationships
- **Driver Approach**: AI sees all responsible modules together, understanding interactions and dependencies
- **Result**: Better enhancement decisions based on complete context

##### 2. **Intelligent Module Selection**:
- **Traditional Problem**: AI often enhanced wrong modules or made unnecessary changes
- **Driver Solution**: AI analyzes all modules together and only updates those with actual functional changes
- **Result**: Precise targeting of modules that need enhancement

##### 3. **Reduced Enhancement Errors**:
- **Traditional Issue**: Individual module enhancement could break module interactions
- **Driver Advantage**: AI understands module relationships and avoids conflicting changes
- **Result**: More reliable and stable module enhancements

##### 4. **Efficient Processing**:
- **Traditional Method**: Multiple AI calls for each responsible module
- **Driver Method**: Single AI enhancement call for all modules combined
- **Result**: Faster processing and reduced AI API costs

##### 5. **Superior Change Detection**:
- **Traditional Detection**: Simple text comparison that flagged comment changes as functional changes
- **Driver Detection**: Intelligent functional analysis that ignores comments and formatting
- **Result**: Only modules with real functional changes get attempt files

##### 6. **Complete Audit Trail**:
- **Traditional Tracking**: Individual module tracking only
- **Driver Tracking**: Dual Excel system with individual results + combined module tracking
- **Result**: Complete visibility into both individual and holistic enhancement process

#### Technical Implementation Benefits:

##### Module Boundary Management:
```python
# Driver module combines all modules with clear boundaries:
# === MODULE: nvl START ===
[nvl module code]
# === MODULE: nvl END ===
# === MODULE: join START ===
[join module code]
# === MODULE: join END ===
```

##### Intelligent Decomposition:
- **Boundary Preservation**: AI maintains exact module boundary markers
- **Functional Analysis**: Compares original vs enhanced for actual code changes
- **Selective Saving**: Only changed modules get attempt files

##### Enhanced Excel Tracking:
- **Main Sheet**: Individual module results (changed/unchanged status)
- **driver_modules Sheet**: Complete combined module tracking with original and enhanced code
- **Analysis Column**: Detailed summary of enhancement results

### Recent Workflow Enhancements:
- **Revolutionary Driver Module Approach**: Implemented intelligent driver module enhancement for superior AI processing
- **Dual Excel Tracking System**: Main sheet for individual results + driver_modules sheet for combined tracking
- **Functional Change Detection**: Advanced analysis that ignores comments and focuses on actual code logic
- **Holistic AI Enhancement**: AI processes all responsible modules together for better context and decisions
- **Selective Module Updates**: Only modules with functional changes get updated, preserving working modules
- **Complete Audit Trail**: Both original and enhanced combined code stored for comprehensive analysis
- **Enhanced Error Handling**: Comprehensive validation and graceful error handling throughout the driver approach
- **Import Standardization**: All imports properly organized at file top following codebase conventions

## Recent Implementation Fixes and Improvements

### 1. Path Formation Resolution:
- **Issue**: Module paths were incorrectly formed with double .py extensions (e.g., `raisenotice.py\Raisenotice.py`)
- **Root Cause**: Code was appending `f"{feature_name}.py"` to paths that already ended with `.py`
- **Solution**: Removed extra filename append since `module_path` already contains complete relative path
- **Result**: Correct path formation like `C:\...\Conversion_Modules\Oracle_Postgres14\Common\Statement\Pre\raisenotice.py`

### 2. Filename Consistency Implementation:
- **Issue**: Reading `raisenotice.py` but saving as `Raisenotice_attempt_1.py` (case mismatch)
- **Root Cause**: Using `feature_name` (capitalized) instead of extracting original filename
- **Solution**: Extract original filename using `os.path.basename()` and `os.path.splitext()`
- **Implementation**:
  ```python
  # Extract original filename from module_path
  original_filename = os.path.basename(module_path)  # 'raisenotice.py'
  base_name = os.path.splitext(original_filename)[0]  # 'raisenotice'
  module_filename = f"{base_name}_attempt_{attempt_number}.py"  # 'raisenotice_attempt_1.py'
  ```
- **Result**: Consistent filename preservation throughout the workflow

### 3. Structured Output Method Correction:
- **Issue**: `'AzureOpenAILLM' object has no attribute 'with_structured_output'`
- **Root Cause**: Inconsistent usage between `self.llm.client.with_structured_output()` and `self.llm.with_structured_output()`
- **Solution**: Standardized all structured output calls to use `self.llm.client.with_structured_output()`
- **Result**: Consistent AI integration across all nodes

### 4. Tuple vs Dictionary Handling:
- **Issue**: Responsible features stored as tuples but validation expecting dictionary format
- **Root Cause**: Mixed data types in validation prompt function
- **Solution**: Enhanced validation prompt to handle tuples, dictionaries, and ResponsibleFeature objects
- **Implementation**:
  ```python
  elif isinstance(feature, tuple) and len(feature) >= 2:
      feature_name = feature[0]
      module_path = feature[1]
      responsibility_reason = "Identified as responsible for conversion issue"
  ```
- **Result**: Robust handling of different data formats

### 5. Migration Name Configuration:
- **Implementation**: Uses migration name directly from request for all file paths and operations
- **Prompt Integration**: Splits migration name on underscore to extract source and target database names for prompts
- **Path Consistency**: Ensures all file operations use the same migration name context

## Key Enhancements

### 1. Stage 1 Metadata Integration:
- Added `migration_name` and `objecttype` as input parameters
- File paths now use Stage 1 metadata structure: `Stage1_Metadata/migration_name/schema_name/objecttype/object_name`
- CSV files generated from Stage 1 with approved statements and metadata
- Conversion_Files.xlsx available at schema level: `Stage1_Metadata/migration_name/schema_name/`
- Pattern-based file copying: All files ending with `_Package_Extraction.csv` copied to schema level
- Module paths reference migration-specific files from Stage 1 metadata generation

### 2. Enhanced AI Mapping:
- **Smart statement matching** between approved (5) and object-level (50) statements
- **AI-driven similarity detection** beyond just statement numbers
- **Robust validation** ensuring correct feature mapping

### 3. Detailed Module Management:
- **Conversion_Modules folder structure**: `Conversion_Modules/{migration_name}/{object_path}/{feature_name}.py`
- **CSV-based path resolution**: Feature paths stored in `Conversion_Modules/{migration_name}/{migration_name}.csv`
- **Migration-specific paths**: Each migration has its own module folder structure and CSV mapping
- **Real example**: `Conversion_Modules/Oracle_Postgres14/Common/Statement/Pre/nvl.py`
- **Mixed module application**: Modified modules + original QMigrator modules
- **Path-based module resolution**: Using `migration_name` and CSV lookup for `Object_Path` mapping

### 4. Improved Error Handling:
- **5-attempt limit per statement** with latest attempt (Attempt 5) considered as correct
- **Granular retry mechanisms** at each validation point
- **Comprehensive retry mechanisms** ensuring quality at each step
- **Latest attempt acceptance** when max attempts reached

## Retry Mechanisms

1. **AI Statement Comparison** → Module Updates retry (when statements don't match)
2. **Statement Processing** → 5 attempt limit per statement (Attempt 5 accepted as correct)
3. **More Statements Decision** → Next statement or retry current statement based on results

## Manual Review Interface (Separate UI Application)

### Frontend React Application Structure
The manual review interface is a separate UI application that uses the Stage 2 workflow data:

#### Selection Criteria:
- **Process Type**: qmigrator or qbook
- **Migration Context**: migration_name selection
- **Schema Object**: schema_name/objecttype/object_name selection
- **Statement List**: Display all statements for selected object from Stage 1 metadata

#### Statement Management Structure:
```
Statement 1
├── Attempt 1 (Interactive) ⬇️
│   ├── 📁 xml (Feature 1)
│   │   ├── Object Path: Common/Statement/Pre
│   │   ├── Original Code: [Collapsible code block]
│   │   ├── Modified Code: [Collapsible code block]
│   │   └── Actions: Save/Approve/Reject
│   ├── 📁 nvl (Feature 2)
│   │   ├── Object Path: Common/Statement/Pre
│   │   ├── Original Code: [Collapsible code block]
│   │   ├── Modified Code: [Collapsible code block]
│   │   └── Actions: Save/Approve/Reject
│   ├── 📁 sysdate (Feature 3)
│   │   └── [Similar structure]
│   └── 🔄 Merge All Approved (Button)
├── Attempt 2 (Reference Only) ⬇️
│   ├── 📁 xml (Feature 1) - Read Only
│   ├── 📁 decode (Feature 2) - Read Only
│   └── 📁 to_char (Feature 3) - Read Only
├── Attempt 3 (Reference Only) ⬇️
│   └── [Multiple modules - Display only for reference]
├── Attempt 4 (Reference Only) ⬇️
│   └── [Multiple modules - Display only for reference]
├── Attempt 5 (Reference Only - Latest/Correct) ⬇️
│   └── [Multiple modules - Display only - considered as correct when max attempts reached]
└── 🔄 Merge Final (Upload to QBook site - Available for Attempt 5)

Statement 2, 3, 4, 5...
└── [Similar structure]
```

#### Action Definitions:
- **Save**: Store changes to database
- **Approve**: Mark module as approved for production use
- **Reject**: Mark module as rejected, requiring rework
- **Merge**: Upload approved module to QBook site (only for Attempt 1)
- **Attempts 2-5**: Display only for reference, no interactive actions

#### Data Retrieval Queries:
```sql
-- Get statements by process type and object
SELECT * FROM stage2_statements
WHERE process_type = ? AND schema_name = ? AND tgt_object_name = ?
ORDER BY statement_id;

-- Get conversion modules by statement
SELECT * FROM stage2_conversion_modules
WHERE statement_id = ?
ORDER BY Attempt, Feature_Name;
```

#### Key Features:
- **Attempt 1**: Full interactive capabilities with Save/Approve/Reject/Merge
- **Attempts 2-5**: Read-only display for reference and learning
- **Attempt 5 Special**: Considered as correct when max attempts reached
- **Merge Functionality**: Available for approved Attempt 1 modules or Attempt 5 (when max attempts reached)
- **QBook Integration**: Direct upload capability for approved modules

This enhanced workflow provides robust processing with detailed AI validation, comprehensive module management, and migration-specific context handling. The separate manual review interface allows human oversight and approval of AI-generated module updates.

## Summary

### Workflow Overview
This comprehensive Stage 2 workflow provides robust QMigrator module enhancement capabilities with intelligent AI-driven analysis, comprehensive error handling, iterative improvement mechanisms, and seamless integration with Stage 1 for optimal conversion results and continuous system improvement.

### Key Business Benefits
- **Root Cause Resolution**: Fixes underlying QMigrator conversion issues rather than just symptoms
- **Scalable Improvement**: Each module update benefits all future conversions
- **Quality Assurance**: Systematic validation ensures reliable module updates
- **Cost Effectiveness**: Reduces manual intervention needs over time
- **Knowledge Preservation**: Captures conversion expertise in reusable code

### Technical Achievements
- **Streamlined Implementation**: All 10 workflow nodes fully implemented and production-ready
- **AI Integration**: Advanced AI analysis with structured outputs and feedback loops
- **Robust Architecture**: Comprehensive error handling and graceful degradation
- **Audit Trail**: Complete Excel logging and state management for full traceability
- **Environment Flexibility**: Support for both local and cloud deployments

### Workflow Capabilities
- **Dual Processing Modes**: Support for both object-level (QMigrator) and statement-level (QBook) processing
- **Statement-by-Statement Processing**: Focused analysis with up to 5 retry attempts per statement
- **Intelligent Validation**: AI-driven validation with feedback loops for continuous improvement
- **Module Version Control**: Attempt-based versioning for tracking changes and rollback capability
- **Comprehensive Logging**: Multi-sheet Excel tracking for complete audit and analysis

This Stage 2 workflow represents a sophisticated, production-ready solution for systematically improving QMigrator's Oracle-to-PostgreSQL conversion accuracy through AI-driven module enhancement and validation.
